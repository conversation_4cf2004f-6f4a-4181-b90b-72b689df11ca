import { useState, useEffect, type <PERSON>actNode } from 'react'
import { apiService } from '../services/api'
import { AuthContext, type AuthContextType } from './AuthContextTypes'

interface AuthProviderProps {
  children: ReactNode
}

export const AuthProvider: React.FC<AuthProviderProps> = ({ children }) => {
  const [user, setUser] = useState<User | null>(null)
  const [permissions, setPermissions] = useState<UserPermissions | null>(null)
  const [isLoading, setIsLoading] = useState(true)

  const isAuthenticated = !!user

  // Initialize auth state from localStorage
  useEffect(() => {
    const initializeAuth = async () => {
      try {
        const token = localStorage.getItem('token')
        const storedUser = localStorage.getItem('user')

        if (token && storedUser) {
          const userData = JSON.parse(storedUser)
          setUser(userData)

          // Fetch fresh permissions
          await refreshPermissions()
        }
      } catch (error) {
        console.error('Error initializing auth:', error)
        // Clear invalid data
        localStorage.removeItem('token')
        localStorage.removeItem('user')
      } finally {
        setIsLoading(false)
      }
    }

    initializeAuth()
  }, [])

  const login = async (credentials: LoginRequest): Promise<{ success: boolean; message: string }> => {
    try {
      setIsLoading(true)
      const response = await apiService.login(credentials)
      console.log('LOGIN RESPONSE', response)
      if (response.success && response.data.token && response.data.user) {
        // Store token and user data
        localStorage.setItem('token', response.data.token)
        localStorage.setItem('user', JSON.stringify(response.data.user))

        setUser(response.data.user)

        // Fetch user permissions
        await refreshPermissions()

        return { success: true, message: response.message }
      } else {
        return { success: false, message: response.message || 'Login failed' }
      }
    } catch (err: unknown) {
      console.error('Login error:', err)
      return {
        success: false,
        message:
          err instanceof Error && 'response' in err
            ? (err as { response?: { data?: { message?: string } } }).response?.data?.message || 'Login failed'
            : 'Login failed'
        // error.response?.data?.message || 'Login failed. Please try again.'
      }
    } finally {
      setIsLoading(false)
    }
  }

  const register = async (userData: RegisterRequest): Promise<{ success: boolean; message: string }> => {
    try {
      setIsLoading(true)
      const response = await apiService.register(userData)

      if (response.success && response.data.token && response.data.user) {
        // Store token and user data
        localStorage.setItem('token', response.data.token)
        localStorage.setItem('user', JSON.stringify(response.data.user))

        setUser(response.data.user)

        // Fetch user permissions
        await refreshPermissions()

        return { success: true, message: response.message }
      } else {
        return { success: false, message: response.message || 'Registration failed' }
      }
    } catch (err: unknown) {
      console.error('Registration error:', err)
      return {
        success: false,
        message:
          err instanceof Error && 'response' in err
            ? (err as { response?: { data?: { message?: string } } }).response?.data?.message || 'Registration failed'
            : 'Registration failed'
      }
    } finally {
      setIsLoading(false)
    }
  }

  const logout = () => {
    localStorage.removeItem('token')
    localStorage.removeItem('user')
    setUser(null)
    setPermissions(null)
  }

  const refreshPermissions = async (): Promise<void> => {
    try {
      const response = await apiService.getUserPermissions()
      if (response.success && response.data) {
        setPermissions(response.data)
      }
    } catch (error) {
      console.error('Error fetching permissions:', error)
      setPermissions(null)
    }
  }

  const hasPermission = (module: string, action: string): boolean => {
    if (!permissions || !permissions.permissions) {
      return false
    }

    return permissions.permissions.some(permission => permission.module === module && permission.action === action)
  }

  const value: AuthContextType = {
    user,
    permissions,
    isAuthenticated,
    isLoading,
    login,
    register,
    logout,
    refreshPermissions,
    hasPermission
  }

  return <AuthContext.Provider value={value}>{children}</AuthContext.Provider>
}
